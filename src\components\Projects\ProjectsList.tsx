import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Plus, Folder, Calendar, Users, FolderOpen } from 'lucide-react';
import { useProjects } from '@/hooks/useData';

const ProjectsList: React.FC = () => {
  const { t } = useLanguage();

  // Fetch real projects data
  const { data: projectsResult, isLoading, error } = useProjects({}, { page: 1, limit: 50 });

  const projects = projectsResult?.data || [];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-10 bg-muted rounded w-32"></div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="h-48 bg-muted rounded-xl"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-destructive">{t('common.error')}</p>
          <p className="text-muted-foreground mt-2">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">{t('projects.allProjects')}</h2>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          {t('projects.newProject')}
        </Button>
      </div>

      {projects.length === 0 ? (
        <div className="text-center py-12">
          <FolderOpen className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">{t('projects.empty.title')}</h3>
          <p className="text-muted-foreground mb-4">{t('projects.empty.description')}</p>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            {t('projects.newProject')}
          </Button>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {projects.map((project) => (
          <Card key={project.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <Folder className="w-5 h-5 text-primary" />
                  <CardTitle className="text-lg">{project.name}</CardTitle>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground line-clamp-2">
                {project.description}
              </p>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">
                  {t('projects.tasks')}: {project.completedTasks}/{project.tasksCount}
                </span>
                <div className="flex items-center text-muted-foreground">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(project.dueDate).toLocaleDateString()}
                </div>
              </div>

              <div className="w-full bg-secondary rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all"
                  style={{ 
                    width: `${(project.completedTasks / project.tasksCount) * 100}%` 
                  }}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {projects.length === 0 && (
        <div className="text-center py-12">
          <Folder className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">{t('projects.noProjects')}</h3>
          <p className="text-muted-foreground mb-4">{t('projects.noProjectsDescription')}</p>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            {t('projects.createFirst')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ProjectsList;
