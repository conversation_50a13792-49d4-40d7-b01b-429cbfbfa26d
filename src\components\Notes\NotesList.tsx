import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, FileText, Pin, Calendar, StickyNote } from 'lucide-react';
import { useNotes } from '@/hooks/useData';

const NotesList: React.FC = () => {
  const { t } = useLanguage();

  // Fetch real notes data
  const { data: notesResult, isLoading, error } = useNotes({}, { page: 1, limit: 50 });

  const notes = notesResult?.data || [];

  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-10 bg-muted rounded w-32"></div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="h-48 bg-muted rounded-xl"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-destructive">{t('common.error')}</p>
          <p className="text-muted-foreground mt-2">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">{t('notes.allNotes')}</h2>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          {t('notes.newNote')}
        </Button>
      </div>

      {notes.length === 0 ? (
        <div className="text-center py-12">
          <StickyNote className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">{t('notes.empty.title')}</h3>
          <p className="text-muted-foreground mb-4">{t('notes.empty.description')}</p>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            {t('notes.newNote')}
          </Button>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {notes.map((note) => (
          <Card key={note.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2 flex-1">
                  <FileText className="w-5 h-5 text-primary flex-shrink-0" />
                  <CardTitle className="text-lg line-clamp-1">{note.title}</CardTitle>
                </div>
                {note.isPinned && (
                  <Pin className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground line-clamp-3">
                {note.content}
              </p>
              
              {note.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {note.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {note.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{note.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}

              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDate(note.updatedAt)}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {notes.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">{t('notes.noNotes')}</h3>
          <p className="text-muted-foreground mb-4">{t('notes.noNotesDescription')}</p>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            {t('notes.createFirst')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default NotesList;
